#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""自定义打印模块

提供不同级别的日志输出功能。
"""

import datetime
from typing import Any

def _get_timestamp() -> str:
    """获取当前时间戳"""
    return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def info(message: Any) -> None:
    """输出信息级别日志"""
    print(f"[{_get_timestamp()}] [INFO] {message}")

def error(message: Any) -> None:
    """输出错误级别日志"""
    print(f"[{_get_timestamp()}] [ERROR] {message}")

def warning(message: Any) -> None:
    """输出警告级别日志"""
    print(f"[{_get_timestamp()}] [WARNING] {message}")

def debug(message: Any) -> None:
    """输出调试级别日志"""
    print(f"[{_get_timestamp()}] [DEBUG] {message}")
