#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""增强版浏览器管理工具模块

在原有基础上增加更强的反检测能力，专门针对Cloudflare Turnstile验证。
"""

from DrissionPage import ChromiumOptions, Chromium
import os
import sys
import traceback
import tempfile
import shutil
import signal
import time
import random
from typing import Optional, Dict, Any, Union
from custom_print import info, error, warning, debug
import atexit

# -------------------------------------------------------------------------
# 增强的常量定义
# -------------------------------------------------------------------------

# 增强版Turnstile Patch 配置内容
ENHANCED_TURNSTILE_MANIFEST_JSON = '''
{
    "manifest_version": 3,
    "name": "Enhanced Turnstile Patcher",
    "version": "3.0",
    "content_scripts": [
        {
            "js": [
                "./anti-detect.js",
                "./turnstile-patch.js"
            ],
            "matches": [
                "<all_urls>"
            ],
            "run_at": "document_start",
            "all_frames": true,
            "world": "MAIN"
        }
    ],
    "permissions": [
        "webNavigation"
    ]
}
'''

# 反检测脚本
ANTI_DETECT_SCRIPT_JS = '''
// 反WebDriver检测
Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
});

// 移除自动化相关属性
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

// 伪造Chrome运行时
window.chrome = {
    runtime: {
        onConnect: undefined,
        onMessage: undefined
    }
};

// 重写权限查询
const originalQuery = window.navigator.permissions.query;
window.navigator.permissions.query = (parameters) => (
    parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
);

// 伪造插件信息
Object.defineProperty(navigator, 'plugins', {
    get: () => [
        {
            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
            description: "Portable Document Format",
            filename: "internal-pdf-viewer",
            length: 1,
            name: "Chrome PDF Plugin"
        }
    ],
});

// 重写语言属性
Object.defineProperty(navigator, 'languages', {
    get: () => ['en-US', 'en'],
});

// 伪造硬件并发
Object.defineProperty(navigator, 'hardwareConcurrency', {
    get: () => 4,
});

// 重写用户代理
const originalUserAgent = navigator.userAgent;
Object.defineProperty(navigator, 'userAgent', {
    get: () => originalUserAgent.replace(/HeadlessChrome/, 'Chrome'),
});

// 伪造屏幕信息
Object.defineProperty(screen, 'availHeight', {
    get: () => 1040,
});
Object.defineProperty(screen, 'availWidth', {
    get: () => 1920,
});

console.log('Anti-detection script loaded');
'''

# 增强版Turnstile绕过脚本
ENHANCED_TURNSTILE_SCRIPT_JS = '''
// 生成更真实的随机坐标
function getRealisticCoordinates() {
    const screenWidth = window.screen.width || 1920;
    const screenHeight = window.screen.height || 1080;
    
    // 基于实际屏幕尺寸生成坐标
    const x = Math.floor(Math.random() * (screenWidth * 0.8)) + (screenWidth * 0.1);
    const y = Math.floor(Math.random() * (screenHeight * 0.8)) + (screenHeight * 0.1);
    
    return { x, y };
}

// 模拟真实的鼠标移动轨迹
function simulateMouseMovement() {
    const coords = getRealisticCoordinates();
    
    // 重写MouseEvent的坐标属性
    Object.defineProperty(MouseEvent.prototype, 'screenX', { 
        value: coords.x,
        writable: false 
    });
    Object.defineProperty(MouseEvent.prototype, 'screenY', { 
        value: coords.y,
        writable: false 
    });
    Object.defineProperty(MouseEvent.prototype, 'clientX', { 
        value: coords.x - window.screenX,
        writable: false 
    });
    Object.defineProperty(MouseEvent.prototype, 'clientY', { 
        value: coords.y - window.screenY,
        writable: false 
    });
}

// 模拟人类行为模式
function simulateHumanBehavior() {
    // 随机延迟
    setTimeout(() => {
        simulateMouseMovement();
    }, Math.random() * 1000 + 500);
    
    // 模拟页面焦点事件
    window.addEventListener('load', () => {
        setTimeout(() => {
            window.focus();
            document.body.click();
        }, Math.random() * 2000 + 1000);
    });
}

// 拦截和修改Turnstile相关请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('challenges.cloudflare.com')) {
        console.log('Intercepting Cloudflare challenge request:', url);
        
        // 添加延迟模拟人类行为
        return new Promise(resolve => {
            setTimeout(() => {
                resolve(originalFetch.apply(this, args));
            }, Math.random() * 1000 + 500);
        });
    }
    return originalFetch.apply(this, args);
};

// 初始化
simulateHumanBehavior();

console.log('Enhanced Turnstile patch loaded');
'''

# 保存所有活跃的浏览器管理器实例
_active_browser_managers = []

# -------------------------------------------------------------------------
# 异常类定义
# -------------------------------------------------------------------------

class BrowserError(Exception):
    """浏览器相关错误的基类"""
    pass

class BrowserInitError(BrowserError):
    """浏览器初始化错误"""
    pass

class BrowserConfigError(BrowserError):
    """浏览器配置错误"""
    pass

class ExtensionError(BrowserError):
    """浏览器扩展相关错误"""
    pass

# -------------------------------------------------------------------------
# 信号处理函数
# -------------------------------------------------------------------------

def _exit_handler(signum=None, frame=None):
    """处理退出信号，确保所有浏览器实例被关闭"""
    debug(f"收到退出信号: {signum if signum else 'atexit'}, 开始清理所有浏览器资源...")
    
    for manager in list(_active_browser_managers):
        try:
            if manager and not manager.is_released():
                debug("正在强制关闭浏览器...")
                try:
                    if manager.browser:
                        # 先关闭所有标签页
                        if hasattr(manager.browser, 'tabs') and manager.browser.tabs:
                            for tab in list(manager.browser.tabs):
                                try:
                                    if tab and hasattr(tab, 'quit'):
                                        tab.quit()
                                except Exception as e:
                                    debug(f"关闭标签页时出错: {str(e)}")
                        
                        # 短暂延时
                        import time
                        time.sleep(0.5)
                        
                        # 关闭浏览器
                        manager.browser.quit()
                except Exception as e:
                    debug(f"强制关闭浏览器时出错: {str(e)}")
                finally:
                    manager._is_browser_released = True
        except Exception as e:
            error(f"清理浏览器资源时发生错误: {str(e)}")
    
    debug("所有浏览器资源清理完成")

# 注册信号处理
for sig in [signal.SIGINT, signal.SIGTERM]:
    try:
        signal.signal(sig, _exit_handler)
    except (AttributeError, ValueError):
        # 某些平台可能不支持特定信号
        pass

# 注册atexit处理
atexit.register(_exit_handler)

# -------------------------------------------------------------------------
# 增强版浏览器管理器类
# -------------------------------------------------------------------------

class EnhancedBrowserManager:
    """增强版浏览器管理器

    专门针对Cloudflare Turnstile验证的反检测浏览器管理器。
    在原有功能基础上增加了更强的反检测能力。
    """

    def __init__(self):
        """初始化增强版浏览器管理器"""
        self.browser = None
        self._is_browser_released = False
        self._temp_extension_dir = None
        self._atexit_registered = False

        # 注册到全局活跃实例列表
        _active_browser_managers.append(self)

    def __enter__(self):
        """上下文管理器入口方法"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出方法"""
        self.quit()
        return False

    def init_browser(self, user_agent: Optional[str] = None, headless_value: Optional[bool] = None) -> Chromium:
        """初始化增强版浏览器实例

        Args:
            user_agent: 自定义用户代理字符串
            headless_value: 是否使用无头模式，为None时使用环境变量配置

        Returns:
            Chromium: 初始化好的浏览器实例

        Raises:
            BrowserInitError: 浏览器初始化失败
            BrowserConfigError: 浏览器配置错误
        """
        if sys.platform not in ["win32", "darwin"]:
            raise NotImplementedError(f"不支持的操作系统: {sys.platform}")

        if self.browser and not self._is_browser_released:
            debug("浏览器已经初始化，将返回现有实例")
            return self.browser

        self._is_browser_released = False

        try:
            debug("开始初始化增强版浏览器配置...")
            co = self._get_enhanced_browser_options(user_agent, headless_value)

            debug("正在创建增强版浏览器实例...")
            self.browser = Chromium(co)

            if not self.browser:
                self._is_browser_released = True
                raise BrowserInitError("浏览器实例创建失败")

            # 验证浏览器运行状态
            try:
                debug("正在验证浏览器运行状态...")
                self.browser.latest_tab.get("about:blank")

                # 等待扩展加载
                time.sleep(2)

                # 执行额外的反检测脚本
                self._inject_additional_scripts()

            except Exception as e:
                error(f"浏览器运行状态验证失败: {str(e)}")
                if self.browser and not self._is_browser_released:
                    try:
                        self.browser.quit()
                        self._is_browser_released = True
                    except Exception as quit_error:
                        error(f"关闭浏览器时发生错误: {str(quit_error)}")
                raise BrowserInitError(f"浏览器运行状态验证失败: {str(e)}")

            info(f"增强版浏览器初始化成功")
            return self.browser

        except Exception as e:
            error_msg = f"增强版浏览器初始化失败: {str(e)}"
            error(error_msg)
            if self.browser and not self._is_browser_released:
                try:
                    self.browser.quit()
                    self._is_browser_released = True
                except Exception as quit_error:
                    error(f"关闭浏览器时发生错误: {str(quit_error)}")
            raise BrowserInitError(error_msg) from e

    def _get_enhanced_browser_options(self, user_agent: Optional[str] = None, headless_value: Optional[bool] = None) -> ChromiumOptions:
        """获取增强版浏览器配置选项"""
        try:
            debug("正在配置增强版浏览器选项...")
            co = ChromiumOptions()

            # 加载增强版扩展
            try:
                extension_path = self._get_enhanced_extension_path()
                co.add_extension(extension_path)
                debug(f"成功加载增强版浏览器扩展: {extension_path}")
            except Exception as e:
                error(f"加载增强版浏览器扩展时发生错误: {str(e)}")
                raise BrowserConfigError(f"增强版浏览器扩展配置失败: {str(e)}")

            # 反检测配置
            co.set_argument("--disable-blink-features=AutomationControlled")
            co.set_argument("--disable-dev-shm-usage")
            co.set_argument("--disable-extensions-except=" + self._temp_extension_dir if self._temp_extension_dir else "")
            co.set_argument("--disable-plugins-discovery")
            co.set_argument("--disable-default-apps")
            co.set_argument("--disable-background-timer-throttling")
            co.set_argument("--disable-backgrounding-occluded-windows")
            co.set_argument("--disable-renderer-backgrounding")
            co.set_argument("--disable-features=TranslateUI")
            co.set_argument("--disable-ipc-flooding-protection")

            # 基本浏览器配置
            co.set_pref("credentials_enable_service", False)
            co.set_pref("profile.password_manager_enabled", False)
            co.set_argument("--hide-crash-restore-bubble")

            # 设置更真实的用户代理
            if not user_agent:
                user_agent = self._get_realistic_user_agent()
            co.set_user_agent(user_agent)
            debug(f"已设置用户代理: {user_agent}")

            # 代理设置
            proxy = os.environ.get("BROWSER_PROXY")
            if proxy:
                try:
                    co.set_proxy(proxy)
                    debug(f"已设置代理: {proxy}")
                except Exception as e:
                    warning(f"设置代理失败: {str(e)}")

            # 端口配置
            try:
                co.auto_port()
            except Exception as e:
                error(f"自动端口配置失败: {str(e)}")
                raise BrowserConfigError(f"端口配置失败: {str(e)}")

            # 无头模式设置
            try:
                if headless_value is not None:
                    is_headless = headless_value if isinstance(headless_value, bool) else headless_value.lower() == "true"
                    co.headless(is_headless)
                else:
                    headless_setting = os.environ.get("BROWSER_HEADLESS", "False").lower() == "true"
                    co.headless(headless_setting)
                debug(f"已设置无头模式: {headless_value if headless_value is not None else headless_setting}")
            except Exception as e:
                error(f"设置无头模式失败: {str(e)}")
                raise BrowserConfigError(f"无头模式配置失败: {str(e)}")

            # Mac 系统特殊处理
            if sys.platform == "darwin":
                try:
                    co.set_argument("--no-sandbox")
                    co.set_argument("--disable-gpu")
                    debug("已应用Mac系统特殊配置")
                except Exception as e:
                    warning(f"Mac系统特殊配置失败: {str(e)}")

            return co

        except Exception as e:
            error_msg = f"增强版浏览器选项配置失败: {str(e)}"
            error(error_msg)
            raise BrowserConfigError(error_msg) from e
