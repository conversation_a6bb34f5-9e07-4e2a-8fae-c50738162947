#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""测试访问Augment Code登录页面的脚本"""

import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import tempfile
import os

def create_anti_detect_extension():
    """创建反检测Chrome扩展"""
    
    # 扩展清单文件
    manifest_json = '''
{
    "manifest_version": 3,
    "name": "Anti-Detection Extension",
    "version": "1.0",
    "content_scripts": [
        {
            "js": ["content.js"],
            "matches": ["<all_urls>"],
            "run_at": "document_start",
            "all_frames": true,
            "world": "MAIN"
        }
    ]
}
'''
    
    # 反检测脚本
    content_js = '''
// 移除webdriver属性
Object.defineProperty(navigator, 'webdriver', {
    get: () => undefined,
});

// 移除自动化相关属性
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

// 伪造Chrome运行时
window.chrome = {
    runtime: {
        onConnect: undefined,
        onMessage: undefined
    }
};

// 重写权限查询
if (navigator.permissions && navigator.permissions.query) {
    const originalQuery = navigator.permissions.query;
    navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
            Promise.resolve({ state: Notification.permission }) :
            originalQuery(parameters)
    );
}

// 伪造插件信息
Object.defineProperty(navigator, 'plugins', {
    get: () => [
        {
            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
            description: "Portable Document Format",
            filename: "internal-pdf-viewer",
            length: 1,
            name: "Chrome PDF Plugin"
        }
    ],
});

// 重写语言属性
Object.defineProperty(navigator, 'languages', {
    get: () => ['en-US', 'en'],
});

// 伪造硬件并发
Object.defineProperty(navigator, 'hardwareConcurrency', {
    get: () => 4,
});

// 模拟真实的鼠标坐标
function getRandomCoordinates() {
    return {
        x: Math.floor(Math.random() * 1200) + 100,
        y: Math.floor(Math.random() * 800) + 100
    };
}

const coords = getRandomCoordinates();

Object.defineProperty(MouseEvent.prototype, 'screenX', {
    value: coords.x,
    writable: false
});

Object.defineProperty(MouseEvent.prototype, 'screenY', {
    value: coords.y,
    writable: false
});

// 拦截Cloudflare请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const url = args[0];
    if (typeof url === 'string' && url.includes('challenges.cloudflare.com')) {
        console.log('Intercepting Cloudflare request:', url);
        // 添加随机延迟
        return new Promise(resolve => {
            setTimeout(() => {
                resolve(originalFetch.apply(this, args));
            }, Math.random() * 1000 + 500);
        });
    }
    return originalFetch.apply(this, args);
};

console.log('Anti-detection extension loaded');
'''
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="anti_detect_")
    
    # 写入文件
    with open(os.path.join(temp_dir, "manifest.json"), "w", encoding="utf-8") as f:
        f.write(manifest_json)
    
    with open(os.path.join(temp_dir, "content.js"), "w", encoding="utf-8") as f:
        f.write(content_js)
    
    return temp_dir

def setup_chrome_driver():
    """设置Chrome驱动器"""
    options = Options()
    
    # 创建反检测扩展
    extension_path = create_anti_detect_extension()
    options.add_argument(f"--load-extension={extension_path}")
    
    # 反检测参数
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-plugins-discovery")
    options.add_argument("--disable-default-apps")
    options.add_argument("--disable-background-timer-throttling")
    options.add_argument("--disable-backgrounding-occluded-windows")
    options.add_argument("--disable-renderer-backgrounding")
    options.add_argument("--disable-features=TranslateUI")
    options.add_argument("--disable-ipc-flooding-protection")
    
    # 设置用户代理
    options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    # 其他设置
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-gpu")
    options.add_argument("--window-size=1920,1080")
    
    # 不使用无头模式，这样更像真实用户
    # options.add_argument("--headless")
    
    # 设置首选项
    prefs = {
        "credentials_enable_service": False,
        "profile.password_manager_enabled": False,
        "profile.default_content_setting_values.notifications": 2
    }
    options.add_experimental_option("prefs", prefs)
    
    # 排除自动化开关
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    return webdriver.Chrome(options=options), extension_path

def test_augment_login():
    """测试访问Augment Code登录页面"""
    driver = None
    extension_path = None
    
    try:
        print("正在设置Chrome驱动器...")
        driver, extension_path = setup_chrome_driver()
        
        # 执行额外的反检测脚本
        driver.execute_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        print("正在访问登录页面...")
        driver.get("https://app.augmentcode.com/account")
        
        # 等待页面加载
        time.sleep(3)
        
        print("页面标题:", driver.title)
        print("当前URL:", driver.current_url)
        
        # 检查是否有验证码元素
        print("\n检查验证码元素...")
        
        # 查找验证码相关元素
        captcha_selectors = [
            ".ulp-captcha-container",
            "#ulp-auth0-v2-captcha",
            ".cf-turnstile",
            "iframe[src*='turnstile']",
            "iframe[src*='cloudflare']",
            "[data-sitekey]"
        ]
        
        found_captcha = False
        for selector in captcha_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"找到验证码元素: {selector}")
                    for i, element in enumerate(elements):
                        print(f"  元素 {i+1}: 可见={element.is_displayed()}, 大小={element.size}")
                        if element.tag_name == "iframe":
                            print(f"    iframe src: {element.get_attribute('src')}")
                    found_captcha = True
            except Exception as e:
                print(f"检查选择器 {selector} 时出错: {e}")
        
        if not found_captcha:
            print("未找到明显的验证码元素")
        
        # 尝试输入邮箱
        print("\n尝试输入邮箱...")
        try:
            email_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='email'], input[name='email'], input[placeholder*='email' i]"))
            )
            
            # 模拟人类输入行为
            email_input.click()
            time.sleep(random.uniform(0.5, 1.5))
            
            test_email = "<EMAIL>"
            for char in test_email:
                email_input.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            print(f"已输入邮箱: {test_email}")
            
            # 等待一下
            time.sleep(2)
            
            # 尝试点击Continue按钮
            print("尝试点击Continue按钮...")
            try:
                continue_btn = driver.find_element(By.CSS_SELECTOR, "button:contains('Continue'), button[type='submit'], input[type='submit']")
                
                # 模拟人类点击行为
                driver.execute_script("arguments[0].scrollIntoView();", continue_btn)
                time.sleep(random.uniform(0.5, 1.0))
                continue_btn.click()
                
                print("已点击Continue按钮")
                
                # 等待响应
                time.sleep(5)
                
                print("点击后的URL:", driver.current_url)
                
                # 再次检查验证码
                print("\n点击后检查验证码状态...")
                for selector in captcha_selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            print(f"验证码元素 {selector}: {len(elements)} 个")
                            for element in elements:
                                if element.is_displayed():
                                    print(f"  可见的验证码元素: {element.tag_name}")
                    except Exception as e:
                        print(f"检查验证码时出错: {e}")
                
            except NoSuchElementException:
                print("未找到Continue按钮")
                
        except TimeoutException:
            print("未找到邮箱输入框")
        
        # 检查控制台日志
        print("\n检查控制台日志...")
        try:
            logs = driver.get_log('browser')
            cloudflare_logs = [log for log in logs if 'cloudflare' in log['message'].lower() or 'turnstile' in log['message'].lower()]
            if cloudflare_logs:
                print("发现Cloudflare相关日志:")
                for log in cloudflare_logs[-5:]:  # 只显示最后5条
                    print(f"  {log['level']}: {log['message']}")
            else:
                print("未发现Cloudflare相关日志")
        except Exception as e:
            print(f"获取控制台日志失败: {e}")
        
        # 保持页面打开一段时间观察
        print("\n保持页面打开30秒以观察验证码行为...")
        time.sleep(30)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        if driver:
            print("正在关闭浏览器...")
            driver.quit()
        
        # 清理临时扩展目录
        if extension_path and os.path.exists(extension_path):
            import shutil
            try:
                shutil.rmtree(extension_path)
                print("已清理临时扩展目录")
            except Exception as e:
                print(f"清理临时目录失败: {e}")

if __name__ == "__main__":
    test_augment_login()
