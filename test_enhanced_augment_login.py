#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""使用增强版浏览器管理器测试Augment Code登录页面"""

import time
import random
from enhanced_browser_manager import EnhancedBrowserManager

# 简单的日志函数
def info(msg):
    print(f"[INFO] {msg}")

def error(msg):
    print(f"[ERROR] {msg}")

def warning(msg):
    print(f"[WARNING] {msg}")

def debug(msg):
    print(f"[DEBUG] {msg}")

def test_augment_login_enhanced():
    """使用增强版浏览器管理器测试登录"""
    
    info("开始使用增强版浏览器管理器测试Augment Code登录...")
    
    with EnhancedBrowserManager() as manager:
        try:
            # 初始化浏览器
            info("正在初始化增强版浏览器...")
            browser = manager.init_browser(headless_value=False)  # 不使用无头模式，便于观察
            
            # 获取主标签页
            tab = browser.latest_tab
            
            # 访问登录页面
            info("正在访问登录页面...")
            tab.get("https://app.augmentcode.com/account")
            
            # 等待页面加载
            time.sleep(3)
            
            info(f"页面标题: {tab.title}")
            info(f"当前URL: {tab.url}")
            
            # 检查验证码元素
            info("检查验证码相关元素...")
            
            # 查找验证码容器
            captcha_selectors = [
                ".ulp-captcha-container",
                "#ulp-auth0-v2-captcha", 
                ".cf-turnstile",
                "iframe[src*='turnstile']",
                "iframe[src*='cloudflare']",
                "[data-sitekey]"
            ]
            
            found_captcha = False
            for selector in captcha_selectors:
                try:
                    elements = tab.eles(selector)
                    if elements:
                        info(f"找到验证码元素: {selector} ({len(elements)}个)")
                        for i, element in enumerate(elements):
                            try:
                                info(f"  元素 {i+1}: 标签={element.tag}, 可见={element.states.is_displayed}")
                                if element.tag == "iframe":
                                    src = element.attr("src")
                                    if src:
                                        info(f"    iframe src: {src}")
                            except Exception as e:
                                debug(f"检查元素属性时出错: {e}")
                        found_captcha = True
                except Exception as e:
                    debug(f"检查选择器 {selector} 时出错: {e}")
            
            if not found_captcha:
                info("未找到明显的验证码元素")
            
            # 模拟人类行为 - 随机移动鼠标
            info("模拟人类行为...")
            try:
                # 随机点击页面空白区域
                tab.run_js("""
                    // 模拟鼠标移动
                    const event = new MouseEvent('mousemove', {
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight,
                        bubbles: true
                    });
                    document.dispatchEvent(event);
                    
                    // 模拟页面焦点
                    window.focus();
                """)
                time.sleep(random.uniform(1, 3))
            except Exception as e:
                warning(f"模拟人类行为失败: {e}")
            
            # 尝试输入邮箱
            info("尝试输入邮箱...")
            try:
                # 查找邮箱输入框
                email_selectors = [
                    "input[type='email']",
                    "input[name='email']", 
                    "input[placeholder*='email' i]",
                    "input[id*='email' i]"
                ]
                
                email_input = None
                for selector in email_selectors:
                    try:
                        email_input = tab.ele(selector, timeout=2)
                        if email_input:
                            info(f"找到邮箱输入框: {selector}")
                            break
                    except Exception:
                        continue
                
                if email_input:
                    # 模拟人类输入行为
                    email_input.click()
                    time.sleep(random.uniform(0.5, 1.5))
                    
                    test_email = "<EMAIL>"
                    
                    # 逐字符输入，模拟真实打字
                    for char in test_email:
                        email_input.input(char)
                        time.sleep(random.uniform(0.05, 0.2))
                    
                    info(f"已输入邮箱: {test_email}")
                    
                    # 等待一下
                    time.sleep(random.uniform(1, 3))
                    
                    # 尝试点击Continue按钮
                    info("尝试点击Continue按钮...")
                    try:
                        continue_selectors = [
                            "button:contains('Continue')",
                            "button[type='submit']",
                            "input[type='submit']",
                            "button:contains('继续')"
                        ]
                        
                        continue_btn = None
                        for selector in continue_selectors:
                            try:
                                continue_btn = tab.ele(selector, timeout=2)
                                if continue_btn:
                                    info(f"找到Continue按钮: {selector}")
                                    break
                            except Exception:
                                continue
                        
                        if continue_btn:
                            # 模拟人类点击行为
                            time.sleep(random.uniform(0.5, 1.0))
                            continue_btn.click()
                            
                            info("已点击Continue按钮")
                            
                            # 等待响应
                            time.sleep(5)
                            
                            info(f"点击后的URL: {tab.url}")
                            
                            # 再次检查验证码状态
                            info("点击后检查验证码状态...")
                            for selector in captcha_selectors:
                                try:
                                    elements = tab.eles(selector)
                                    if elements:
                                        info(f"验证码元素 {selector}: {len(elements)} 个")
                                        for element in elements:
                                            try:
                                                if element.states.is_displayed:
                                                    info(f"  可见的验证码元素: {element.tag}")
                                            except Exception as e:
                                                debug(f"检查元素可见性时出错: {e}")
                                except Exception as e:
                                    debug(f"检查验证码时出错: {e}")
                        else:
                            warning("未找到Continue按钮")
                            
                    except Exception as e:
                        error(f"点击Continue按钮时出错: {e}")
                        
                else:
                    warning("未找到邮箱输入框")
                    
            except Exception as e:
                error(f"输入邮箱过程中出错: {e}")
            
            # 检查控制台日志
            info("检查控制台日志...")
            try:
                # 执行JavaScript获取控制台信息
                console_info = tab.run_js("""
                    // 返回页面上的一些关键信息
                    return {
                        url: window.location.href,
                        title: document.title,
                        hasCloudflareScripts: !!document.querySelector('script[src*="cloudflare"]'),
                        hasTurnstileElements: !!document.querySelector('.cf-turnstile, [data-sitekey]'),
                        captchaContainers: document.querySelectorAll('.ulp-captcha-container, #ulp-auth0-v2-captcha').length
                    };
                """)
                
                info(f"页面信息: {console_info}")
                
            except Exception as e:
                warning(f"获取控制台信息失败: {e}")
            
            # 保持页面打开一段时间观察
            info("保持页面打开60秒以观察验证码行为...")
            
            # 每10秒检查一次验证码状态
            for i in range(6):
                time.sleep(10)
                info(f"第 {i+1} 次检查验证码状态...")
                
                try:
                    # 检查是否有新的验证码元素出现
                    captcha_status = tab.run_js("""
                        const captchaContainer = document.querySelector('.ulp-captcha-container');
                        const captchaDiv = document.querySelector('#ulp-auth0-v2-captcha');
                        
                        return {
                            captchaContainerVisible: captchaContainer ? (captchaContainer.offsetWidth > 0 && captchaContainer.offsetHeight > 0) : false,
                            captchaDivVisible: captchaDiv ? (captchaDiv.offsetWidth > 0 && captchaDiv.offsetHeight > 0) : false,
                            captchaContainerHTML: captchaContainer ? captchaContainer.innerHTML : null,
                            captchaDivHTML: captchaDiv ? captchaDiv.innerHTML : null,
                            iframes: Array.from(document.querySelectorAll('iframe')).map(iframe => ({
                                src: iframe.src,
                                visible: iframe.offsetWidth > 0 && iframe.offsetHeight > 0
                            }))
                        };
                    """)
                    
                    if captcha_status['captchaContainerVisible'] or captcha_status['captchaDivVisible']:
                        info("发现可见的验证码容器!")
                        info(f"验证码状态: {captcha_status}")
                    
                    if captcha_status['iframes']:
                        visible_iframes = [iframe for iframe in captcha_status['iframes'] if iframe['visible']]
                        if visible_iframes:
                            info(f"发现可见的iframe: {visible_iframes}")
                    
                except Exception as e:
                    debug(f"检查验证码状态时出错: {e}")
            
        except Exception as e:
            error(f"测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_augment_login_enhanced()
