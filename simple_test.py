#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""简化版测试脚本"""

import time
from enhanced_browser_manager import EnhancedBrowserManager

def simple_test():
    """简单测试增强版浏览器管理器"""
    print("开始简单测试...")
    
    try:
        with EnhancedBrowserManager() as manager:
            print("正在初始化浏览器...")
            browser = manager.init_browser(headless_value=False)
            
            print("浏览器初始化成功!")
            
            # 获取主标签页
            tab = browser.latest_tab
            
            print("正在访问测试页面...")
            tab.get("https://app.augmentcode.com/account")
            
            print(f"页面标题: {tab.title}")
            print(f"当前URL: {tab.url}")
            
            # 等待10秒观察
            print("等待10秒...")
            time.sleep(10)
            
            print("测试完成!")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
